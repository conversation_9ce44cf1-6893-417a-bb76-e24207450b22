const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { User } = require('../models');
const config = require('../config/config');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

class AuthService {
  // Generate JWT tokens
  generateTokens(userId) {
    const accessToken = jwt.sign(
      { userId },
      config.ACCESS_TOKEN_SECRET,
      { expiresIn: config.ACCESS_TOKEN_EXPIRES_IN }
    );

    const refreshToken = jwt.sign(
      { userId, type: 'refresh' },
      config.REFRESH_TOKEN_SECRET,
      { expiresIn: config.REFRESH_TOKEN_EXPIRES_IN }
    );

    return { accessToken, refreshToken };
  }

  // Verify access token
  verifyAccessToken(token) {
    try {
      return jwt.verify(token, config.ACCESS_TOKEN_SECRET);
    } catch (error) {
      throw new AppError('Invalid access token', 401);
    }
  }

  // Verify refresh token
  verifyRefreshToken(token) {
    try {
      return jwt.verify(token, config.REFRESH_TOKEN_SECRET);
    } catch (error) {
      throw new AppError('Invalid refresh token', 401);
    }
  }

  // Register new user
  async register(userData) {
    try {
      const { name, email, password, role = 'user' } = userData;

      // Check if user already exists
      const existingUser = await User.findOne({ email });
      if (existingUser) {
        throw new AppError('User with this email already exists', 400);
      }

      // Create new user
      const user = new User({
        name,
        email,
        password,
        role
      });

      await user.save();

      // Generate tokens
      const { accessToken, refreshToken } = this.generateTokens(user._id);

      // Save refresh token to user
      await this.saveRefreshToken(user._id, refreshToken);

      logger.info('New user registered:', { userId: user._id, email: user.email });

      return {
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          role: user.role,
          isEmailVerified: user.isEmailVerified
        },
        accessToken,
        refreshToken
      };
    } catch (error) {
      logger.error('Registration error:', error);
      throw error;
    }
  }

  // Login user
  async login(email, password, ipAddress, userAgent) {
    try {
      // Find user and include password
      const user = await User.findOne({ email, isActive: true }).select('+password +loginAttempts +lockUntil');
      
      if (!user) {
        throw new AppError('Invalid email or password', 401);
      }

      // Check if account is locked
      if (user.isLocked) {
        throw new AppError('Account is temporarily locked due to too many failed login attempts', 423);
      }

      // Check password
      const isPasswordCorrect = await user.correctPassword(password, user.password);
      
      if (!isPasswordCorrect) {
        await user.incLoginAttempts();
        throw new AppError('Invalid email or password', 401);
      }

      // Reset login attempts on successful login
      if (user.loginAttempts > 0) {
        await user.resetLoginAttempts();
      }

      // Update last login
      user.lastLogin = new Date();
      await user.save();

      // Generate tokens
      const { accessToken, refreshToken } = this.generateTokens(user._id);

      // Save refresh token
      await this.saveRefreshToken(user._id, refreshToken);

      logger.info('User logged in:', { 
        userId: user._id, 
        email: user.email, 
        ipAddress, 
        userAgent 
      });

      return {
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          role: user.role,
          isEmailVerified: user.isEmailVerified,
          lastLogin: user.lastLogin
        },
        accessToken,
        refreshToken
      };
    } catch (error) {
      logger.error('Login error:', error);
      throw error;
    }
  }

  // Refresh access token
  async refreshAccessToken(refreshToken) {
    try {
      const decoded = this.verifyRefreshToken(refreshToken);
      
      // Find user and check if refresh token exists and is valid
      const user = await User.findById(decoded.userId);
      if (!user || !user.isActive) {
        throw new AppError('User not found or inactive', 401);
      }

      // Check if refresh token exists in user's tokens
      const tokenExists = user.refreshTokens.find(
        t => t.token === refreshToken && !t.isRevoked && t.expiresAt > new Date()
      );

      if (!tokenExists) {
        throw new AppError('Invalid refresh token', 401);
      }

      // Generate new access token
      const accessToken = jwt.sign(
        { userId: user._id },
        config.ACCESS_TOKEN_SECRET,
        { expiresIn: config.ACCESS_TOKEN_EXPIRES_IN }
      );

      return { accessToken };
    } catch (error) {
      logger.error('Token refresh error:', error);
      throw error;
    }
  }

  // Save refresh token to user
  async saveRefreshToken(userId, refreshToken) {
    try {
      const decoded = this.verifyRefreshToken(refreshToken);
      const expiresAt = new Date(decoded.exp * 1000);

      await User.findByIdAndUpdate(userId, {
        $push: {
          refreshTokens: {
            token: refreshToken,
            expiresAt
          }
        }
      });

      // Clean up expired tokens
      await this.cleanupExpiredTokens(userId);
    } catch (error) {
      logger.error('Error saving refresh token:', error);
      throw error;
    }
  }

  // Logout user (revoke refresh token)
  async logout(userId, refreshToken) {
    try {
      await User.findByIdAndUpdate(userId, {
        $set: {
          'refreshTokens.$[elem].isRevoked': true
        }
      }, {
        arrayFilters: [{ 'elem.token': refreshToken }]
      });

      logger.info('User logged out:', { userId });
    } catch (error) {
      logger.error('Logout error:', error);
      throw error;
    }
  }

  // Logout from all devices
  async logoutAll(userId) {
    try {
      await User.findByIdAndUpdate(userId, {
        $set: {
          'refreshTokens.$[].isRevoked': true
        }
      });

      logger.info('User logged out from all devices:', { userId });
    } catch (error) {
      logger.error('Logout all error:', error);
      throw error;
    }
  }

  // Clean up expired refresh tokens
  async cleanupExpiredTokens(userId) {
    try {
      await User.findByIdAndUpdate(userId, {
        $pull: {
          refreshTokens: {
            $or: [
              { expiresAt: { $lt: new Date() } },
              { isRevoked: true }
            ]
          }
        }
      });
    } catch (error) {
      logger.error('Error cleaning up tokens:', error);
    }
  }
}

module.exports = new AuthService();
