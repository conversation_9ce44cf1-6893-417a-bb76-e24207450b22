// Organization types
const ORGANIZATION_TYPES = [
  'Corporate (CSR / ESG / Sustainability Division)',
  'Foundation (Corporate / Family / Philanthropic)',
  'Social Enterprise (Impact-led Business)',
  'NGO / Non-profit Organisation',
  'Impact Investment / ESG Fund',
  'Advisory / Consulting Firm (CSR / ESG / Sustainability)',
  'Other'
];

// Designations
const DESIGNATIONS = [
  'Chief Sustainability Officer (CSO)',
  'Head of CSR/ESG',
  'Director, Sustainability',
  'Manager, CSR Programs',
  'ESG Analyst',
  'Social Impact Specialist',
  'Development Sector Lead',
  'Program Manager (Social Development)',
  'Other'
];

// Social sector themes
const THEMES = [
  'Health and Nutrition',
  'Education & Skill Development',
  'Environment & Sustainability',
  'Gender Equality & Social Inclusion',
  'Livelihoods & Economic Empowerment',
  'Water & Sanitation',
  'Other'
];

// User roles
const USER_ROLES = ['admin', 'moderator', 'user'];

// Organization statuses
const ORGANIZATION_STATUSES = ['draft', 'submitted', 'approved', 'rejected'];

// HTTP status codes
const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  LOCKED: 423,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500
};

// Rate limiting
const RATE_LIMITS = {
  GENERAL: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // requests per window
  },
  API: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 50 // requests per window
  },
  STRICT: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5 // requests per window
  }
};

// Validation constants
const VALIDATION = {
  PASSWORD_MIN_LENGTH: 8,
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 100,
  COMPANY_NAME_MAX_LENGTH: 200,
  OTHER_FIELD_MAX_LENGTH: 100,
  MAX_LOGIN_ATTEMPTS: 5,
  LOCK_TIME: 2 * 60 * 60 * 1000 // 2 hours
};

module.exports = {
  ORGANIZATION_TYPES,
  DESIGNATIONS,
  THEMES,
  USER_ROLES,
  ORGANIZATION_STATUSES,
  HTTP_STATUS,
  RATE_LIMITS,
  VALIDATION
};
