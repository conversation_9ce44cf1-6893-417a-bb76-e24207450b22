const mongoose = require('mongoose');

const organizationSchema = new mongoose.Schema({
  companyName: {
    type: String,
    required: [true, 'Company name is required'],
    trim: true,
    maxlength: [200, 'Company name cannot exceed 200 characters'],
    index: true
  },
  
  organizationType: {
    type: String,
    required: [true, 'Organization type is required'],
    enum: {
      values: [
        'Corporate (CSR / ESG / Sustainability Division)',
        'Foundation (Corporate / Family / Philanthropic)',
        'Social Enterprise (Impact-led Business)',
        'NGO / Non-profit Organisation',
        'Impact Investment / ESG Fund',
        'Advisory / Consulting Firm (CSR / ESG / Sustainability)',
        'Other'
      ],
      message: 'Invalid organization type'
    }
  },
  
  organizationTypeOther: {
    type: String,
    trim: true,
    maxlength: [100, 'Other organization type cannot exceed 100 characters'],
    validate: {
      validator: function(value) {
        // Required only if organizationType is 'Other'
        if (this.organizationType === 'Other') {
          return value && value.trim().length > 0;
        }
        return true;
      },
      message: 'Please specify the organization type when selecting "Other"'
    }
  },
  
  designation: {
    type: String,
    required: [true, 'Designation is required'],
    enum: {
      values: [
        'Chief Sustainability Officer (CSO)',
        'Head of CSR/ESG',
        'Director, Sustainability',
        'Manager, CSR Programs',
        'ESG Analyst',
        'Social Impact Specialist',
        'Development Sector Lead',
        'Program Manager (Social Development)',
        'Other'
      ],
      message: 'Invalid designation'
    }
  },
  
  designationOther: {
    type: String,
    trim: true,
    maxlength: [100, 'Other designation cannot exceed 100 characters'],
    validate: {
      validator: function(value) {
        // Required only if designation is 'Other'
        if (this.designation === 'Other') {
          return value && value.trim().length > 0;
        }
        return true;
      },
      message: 'Please specify the designation when selecting "Other"'
    }
  },
  
  themes: [{
    type: String,
    enum: {
      values: [
        'Health and Nutrition',
        'Education & Skill Development',
        'Environment & Sustainability',
        'Gender Equality & Social Inclusion',
        'Livelihoods & Economic Empowerment',
        'Water & Sanitation',
        'Other'
      ],
      message: 'Invalid theme selected'
    }
  }],
  
  themesOther: [{
    type: String,
    trim: true,
    maxlength: [100, 'Other theme cannot exceed 100 characters']
  }],
  
  termsAccepted: {
    type: Boolean,
    required: [true, 'Terms and conditions must be accepted'],
    validate: {
      validator: function(value) {
        return value === true;
      },
      message: 'Terms and conditions must be accepted'
    }
  },
  
  // Contact Information (optional fields for future use)
  contactEmail: {
    type: String,
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  
  contactPhone: {
    type: String,
    trim: true,
    match: [/^[\+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number']
  },
  
  // Metadata
  status: {
    type: String,
    enum: ['draft', 'submitted', 'approved', 'rejected'],
    default: 'submitted'
  },
  
  submittedAt: {
    type: Date,
    default: Date.now
  },
  
  ipAddress: {
    type: String,
    trim: true
  },
  
  userAgent: {
    type: String,
    trim: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
organizationSchema.index({ companyName: 1, organizationType: 1 });
organizationSchema.index({ submittedAt: -1 });
organizationSchema.index({ status: 1 });

// Virtual for full organization type (including other)
organizationSchema.virtual('fullOrganizationType').get(function() {
  if (this.organizationType === 'Other' && this.organizationTypeOther) {
    return this.organizationTypeOther;
  }
  return this.organizationType;
});

// Virtual for full designation (including other)
organizationSchema.virtual('fullDesignation').get(function() {
  if (this.designation === 'Other' && this.designationOther) {
    return this.designationOther;
  }
  return this.designation;
});

// Pre-save middleware
organizationSchema.pre('save', function(next) {
  // Clean up themes and themesOther arrays
  if (this.themes && this.themes.includes('Other') && this.themesOther) {
    // Remove empty strings from themesOther
    this.themesOther = this.themesOther.filter(theme => theme && theme.trim().length > 0);
  }
  
  next();
});

module.exports = mongoose.model('Organization', organizationSchema);
