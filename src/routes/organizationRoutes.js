const express = require('express');
const {
  createOrganization,
  getOrganization,
  getAllOrganizations,
  updateOrganizationStatus,
  deleteOrganization,
  getOrganizationStats
} = require('../controllers/organizationController');
const { validateOrganization, sanitizeInput } = require('../middleware/validation');
const { apiLimiter, strictLimiter } = require('../middleware/security');

const router = express.Router();

// Apply API rate limiting to all routes
router.use(apiLimiter);

// Public routes
router.post(
  '/register',
  strictLimiter, // More restrictive rate limiting for registration
  sanitizeInput,
  validateOrganization,
  createOrganization
);

// Protected routes (you can add authentication middleware here)
router.get('/stats', getOrganizationStats);
router.get('/:id', getOrganization);
router.get('/', getAllOrganizations);
router.patch('/:id/status', updateOrganizationStatus);
router.delete('/:id', deleteOrganization);

module.exports = router;
