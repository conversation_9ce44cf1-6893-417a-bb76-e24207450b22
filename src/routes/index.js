const express = require('express');
const organizationRoutes = require('./organizationRoutes');
const authRoutes = require('./authRoutes');

const router = express.Router();

// Health check endpoint
router.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'API is running',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// API routes
router.use('/auth', authRoutes);
router.use('/organizations', organizationRoutes);

// API documentation endpoint
router.get('/docs', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Impact Leader API Documentation',
    version: '1.0.0',
    endpoints: {
      health: 'GET /api/health',
      auth: {
        register: 'POST /api/auth/register',
        login: 'POST /api/auth/login',
        refreshToken: 'POST /api/auth/refresh-token',
        logout: 'POST /api/auth/logout',
        logoutAll: 'POST /api/auth/logout-all',
        profile: 'GET /api/auth/profile',
        updateProfile: 'PATCH /api/auth/profile'
      },
      organizations: {
        register: 'POST /api/organizations/register',
        getAll: 'GET /api/organizations',
        getById: 'GET /api/organizations/:id',
        updateStatus: 'PATCH /api/organizations/:id/status',
        delete: 'DELETE /api/organizations/:id',
        stats: 'GET /api/organizations/stats'
      }
    },
    rateLimit: {
      general: '100 requests per 15 minutes',
      api: '50 requests per 15 minutes',
      registration: '5 requests per 15 minutes'
    }
  });
});

module.exports = router;
