const { Organization } = require('../models');
const { AppError } = require('../middleware/errorHandler');
const logger = require('../utils/logger');

// Create a new organization registration
const createOrganization = async (req, res, next) => {
  try {
    const organizationData = {
      companyName: req.body.companyName,
      organizationType: req.body.organizationType,
      organizationTypeOther: req.body.organizationTypeOther,
      designation: req.body.designation,
      designationOther: req.body.designationOther,
      themes: req.body.themes,
      themesOther: req.body.themesOther,
      termsAccepted: req.body.termsAccepted,
      contactEmail: req.body.contactEmail,
      contactPhone: req.body.contactPhone,
      ipAddress: req.body.ipAddress,
      userAgent: req.body.userAgent
    };

    const organization = new Organization(organizationData);
    await organization.save();

    logger.info('New organization registered:', {
      id: organization._id,
      companyName: organization.companyName,
      organizationType: organization.organizationType,
      ip: req.ip
    });

    res.status(201).json({
      success: true,
      message: 'Organization registered successfully',
      data: {
        id: organization._id,
        companyName: organization.companyName,
        organizationType: organization.fullOrganizationType,
        designation: organization.fullDesignation,
        themes: organization.themes,
        submittedAt: organization.submittedAt,
        status: organization.status
      }
    });
  } catch (error) {
    logger.error('Error creating organization:', error);
    next(error);
  }
};

// Get organization by ID
const getOrganization = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    const organization = await Organization.findById(id);
    
    if (!organization) {
      return next(new AppError('Organization not found', 404));
    }

    res.status(200).json({
      success: true,
      data: organization
    });
  } catch (error) {
    logger.error('Error fetching organization:', error);
    next(error);
  }
};

// Get all organizations with pagination and filtering
const getAllOrganizations = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    // Build filter object
    const filter = {};
    
    if (req.query.organizationType) {
      filter.organizationType = req.query.organizationType;
    }
    
    if (req.query.designation) {
      filter.designation = req.query.designation;
    }
    
    if (req.query.status) {
      filter.status = req.query.status;
    }
    
    if (req.query.themes) {
      filter.themes = { $in: req.query.themes.split(',') };
    }

    // Date range filter
    if (req.query.startDate || req.query.endDate) {
      filter.submittedAt = {};
      if (req.query.startDate) {
        filter.submittedAt.$gte = new Date(req.query.startDate);
      }
      if (req.query.endDate) {
        filter.submittedAt.$lte = new Date(req.query.endDate);
      }
    }

    // Search by company name
    if (req.query.search) {
      filter.companyName = { $regex: req.query.search, $options: 'i' };
    }

    const organizations = await Organization.find(filter)
      .sort({ submittedAt: -1 })
      .skip(skip)
      .limit(limit)
      .select('-ipAddress -userAgent'); // Exclude sensitive fields

    const total = await Organization.countDocuments(filter);
    const totalPages = Math.ceil(total / limit);

    res.status(200).json({
      success: true,
      data: organizations,
      pagination: {
        currentPage: page,
        totalPages,
        totalRecords: total,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    });
  } catch (error) {
    logger.error('Error fetching organizations:', error);
    next(error);
  }
};

// Update organization status
const updateOrganizationStatus = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    const validStatuses = ['draft', 'submitted', 'approved', 'rejected'];
    if (!validStatuses.includes(status)) {
      return next(new AppError('Invalid status', 400));
    }

    const organization = await Organization.findByIdAndUpdate(
      id,
      { status },
      { new: true, runValidators: true }
    );

    if (!organization) {
      return next(new AppError('Organization not found', 404));
    }

    logger.info('Organization status updated:', {
      id: organization._id,
      companyName: organization.companyName,
      newStatus: status
    });

    res.status(200).json({
      success: true,
      message: 'Organization status updated successfully',
      data: organization
    });
  } catch (error) {
    logger.error('Error updating organization status:', error);
    next(error);
  }
};

// Delete organization
const deleteOrganization = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    const organization = await Organization.findByIdAndDelete(id);
    
    if (!organization) {
      return next(new AppError('Organization not found', 404));
    }

    logger.info('Organization deleted:', {
      id: organization._id,
      companyName: organization.companyName
    });

    res.status(200).json({
      success: true,
      message: 'Organization deleted successfully'
    });
  } catch (error) {
    logger.error('Error deleting organization:', error);
    next(error);
  }
};

// Get organization statistics
const getOrganizationStats = async (req, res, next) => {
  try {
    const stats = await Organization.aggregate([
      {
        $group: {
          _id: null,
          totalOrganizations: { $sum: 1 },
          byStatus: {
            $push: {
              status: '$status',
              count: 1
            }
          },
          byOrganizationType: {
            $push: {
              type: '$organizationType',
              count: 1
            }
          }
        }
      },
      {
        $project: {
          _id: 0,
          totalOrganizations: 1,
          statusBreakdown: {
            $reduce: {
              input: '$byStatus',
              initialValue: {},
              in: {
                $mergeObjects: [
                  '$$value',
                  { $arrayToObject: [[{ k: '$$this.status', v: '$$this.count' }]] }
                ]
              }
            }
          },
          typeBreakdown: {
            $reduce: {
              input: '$byOrganizationType',
              initialValue: {},
              in: {
                $mergeObjects: [
                  '$$value',
                  { $arrayToObject: [[{ k: '$$this.type', v: '$$this.count' }]] }
                ]
              }
            }
          }
        }
      }
    ]);

    res.status(200).json({
      success: true,
      data: stats[0] || {
        totalOrganizations: 0,
        statusBreakdown: {},
        typeBreakdown: {}
      }
    });
  } catch (error) {
    logger.error('Error fetching organization stats:', error);
    next(error);
  }
};

module.exports = {
  createOrganization,
  getOrganization,
  getAllOrganizations,
  updateOrganizationStatus,
  deleteOrganization,
  getOrganizationStats
};
