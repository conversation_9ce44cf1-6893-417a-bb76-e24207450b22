const { body, validationResult } = require('express-validator');
const { AppError } = require('./errorHandler');

// Validation middleware to check for validation errors
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => ({
      field: error.path,
      message: error.msg,
      value: error.value
    }));
    
    return next(new AppError(`Validation failed: ${errorMessages.map(e => e.message).join(', ')}`, 400));
  }
  next();
};

// Organization validation rules
const validateOrganization = [
  body('companyName')
    .trim()
    .notEmpty()
    .withMessage('Company name is required')
    .isLength({ min: 2, max: 200 })
    .withMessage('Company name must be between 2 and 200 characters'),

  body('organizationType')
    .notEmpty()
    .withMessage('Organization type is required')
    .isIn([
      'Corporate (CSR / ESG / Sustainability Division)',
      'Foundation (Corporate / Family / Philanthropic)',
      'Social Enterprise (Impact-led Business)',
      'NGO / Non-profit Organisation',
      'Impact Investment / ESG Fund',
      'Advisory / Consulting Firm (CSR / ESG / Sustainability)',
      'Other'
    ])
    .withMessage('Invalid organization type'),

  body('organizationTypeOther')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Other organization type cannot exceed 100 characters')
    .custom((value, { req }) => {
      if (req.body.organizationType === 'Other' && (!value || value.trim().length === 0)) {
        throw new Error('Please specify the organization type when selecting "Other"');
      }
      return true;
    }),

  body('designation')
    .notEmpty()
    .withMessage('Designation is required')
    .isIn([
      'Chief Sustainability Officer (CSO)',
      'Head of CSR/ESG',
      'Director, Sustainability',
      'Manager, CSR Programs',
      'ESG Analyst',
      'Social Impact Specialist',
      'Development Sector Lead',
      'Program Manager (Social Development)',
      'Other'
    ])
    .withMessage('Invalid designation'),

  body('designationOther')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Other designation cannot exceed 100 characters')
    .custom((value, { req }) => {
      if (req.body.designation === 'Other' && (!value || value.trim().length === 0)) {
        throw new Error('Please specify the designation when selecting "Other"');
      }
      return true;
    }),

  body('themes')
    .isArray({ min: 1 })
    .withMessage('At least one theme must be selected')
    .custom((themes) => {
      const validThemes = [
        'Health and Nutrition',
        'Education & Skill Development',
        'Environment & Sustainability',
        'Gender Equality & Social Inclusion',
        'Livelihoods & Economic Empowerment',
        'Water & Sanitation',
        'Other'
      ];
      
      const invalidThemes = themes.filter(theme => !validThemes.includes(theme));
      if (invalidThemes.length > 0) {
        throw new Error(`Invalid themes: ${invalidThemes.join(', ')}`);
      }
      return true;
    }),

  body('themesOther')
    .optional()
    .isArray()
    .withMessage('Themes other must be an array')
    .custom((themesOther, { req }) => {
      if (req.body.themes && req.body.themes.includes('Other')) {
        if (!themesOther || themesOther.length === 0) {
          throw new Error('Please specify other themes when "Other" is selected');
        }
        // Check each theme in themesOther
        themesOther.forEach(theme => {
          if (typeof theme !== 'string' || theme.trim().length === 0) {
            throw new Error('Other themes must be non-empty strings');
          }
          if (theme.length > 100) {
            throw new Error('Each other theme cannot exceed 100 characters');
          }
        });
      }
      return true;
    }),

  body('termsAccepted')
    .isBoolean()
    .withMessage('Terms accepted must be a boolean')
    .equals('true')
    .withMessage('Terms and conditions must be accepted'),

  body('contactEmail')
    .optional()
    .trim()
    .isEmail()
    .withMessage('Please provide a valid email address')
    .normalizeEmail(),

  body('contactPhone')
    .optional()
    .trim()
    .matches(/^[\+]?[1-9][\d]{0,15}$/)
    .withMessage('Please provide a valid phone number'),

  handleValidationErrors
];

// Sanitize input middleware
const sanitizeInput = (req, res, next) => {
  // Add client IP and user agent for tracking
  req.body.ipAddress = req.ip || req.connection.remoteAddress;
  req.body.userAgent = req.get('User-Agent');
  
  next();
};

module.exports = {
  validateOrganization,
  sanitizeInput,
  handleValidationErrors
};
