const Joi = require('joi');
require('dotenv').config();

// Configuration schema for validation
const configSchema = Joi.object({
  PORT: Joi.number().default(3000),
  NODE_ENV: Joi.string().valid('development', 'production', 'test').default('development'),
  
  // Database
  DB_URI: Joi.string().required(),
  
  // Redis
  REDIS_URI: Joi.string().optional(),
  
  // JWT Tokens
  REFRESH_TOKEN_SECRET: Joi.string().required(),
  REFRESH_TOKEN_EXPIRES_IN: Joi.string().default('7d'),
  ACCESS_TOKEN_SECRET: Joi.string().required(),
  ACCESS_TOKEN_EXPIRES_IN: Joi.string().default('15m'),
  
  // Email Configuration
  EmailTo: Joi.string().email().optional(),
  EmailFrom: Joi.string().email().optional(),
  EMAIL_PORT: Joi.number().default(587),
  EMAIL_HOST: Joi.string().optional(),
  EMAIL_USER: Joi.string().optional(),
  EMAIL_PASS: Joi.string().optional(),
  
  // AWS Configuration
  AWS_SECRET_REGION: Joi.string().optional(),
  AWS_ACCESS_KEY_ID: Joi.string().optional(),
  AWS_SECRET_ACCESS_KEY: Joi.string().optional(),
  AWS_ACCESS_BUCKET_NAME: Joi.string().optional(),
  AWS_BUCKET_URI: Joi.string().optional(),
  AWS_ENDPOINT: Joi.string().optional(),
  
  // Firebase Configuration
  FIREBASE_TYPE: Joi.string().optional(),
  FIREBASE_PROJECT_ID: Joi.string().optional(),
  FIREBASE_PROJECT_KEY_ID: Joi.string().optional(),
  FIREBASE_PRIVATE_KEY: Joi.string().optional(),
  FIREBASE_CLIENT_EMAIL: Joi.string().email().optional(),
  FIREBASE_CLIENT_ID: Joi.string().optional(),
  FIREBASE_AUTH_URI: Joi.string().uri().optional(),
  FIREBASE_TOKEN_URI: Joi.string().uri().optional(),
  FIREBASE_AUTH_PROVIDER_X509_CERT_URL: Joi.string().uri().optional(),
  FIREBASE_CLIENT_X509_CERT_URL: Joi.string().uri().optional(),
  FIREBASE_UNIVERSE_DOMAIN: Joi.string().optional(),
}).unknown();

const _config = {
  PORT: process.env.PORT,
  NODE_ENV: process.env.NODE_ENV,
  DB_URI: process.env.DB_URI,
  REDIS_URI: process.env.REDIS_URI,
  REFRESH_TOKEN_SECRET: process.env.REFRESH_TOKEN_SECRET,
  REFRESH_TOKEN_EXPIRES_IN: process.env.REFRESH_TOKEN_EXPIRES_IN,
  ACCESS_TOKEN_SECRET: process.env.ACCESS_TOKEN_SECRET,
  ACCESS_TOKEN_EXPIRES_IN: process.env.ACCESS_TOKEN_EXPIRES_IN,
  EmailTo: process.env.EmailTo,
  EmailFrom: process.env.EmailFrom,
  EMAIL_PORT: process.env.EMAIL_PORT,
  EMAIL_HOST: process.env.EMAIL_HOST,
  EMAIL_USER: process.env.EMAIL_USER,
  EMAIL_PASS: process.env.EMAIL_PASS,
  AWS_SECRET_REGION: process.env.AWS_SECRET_REGION,
  AWS_ACCESS_KEY_ID: process.env.AWS_ACCESS_KEY_ID,
  AWS_SECRET_ACCESS_KEY: process.env.AWS_SECRET_ACCESS_KEY,
  AWS_ACCESS_BUCKET_NAME: process.env.AWS_ACCESS_BUCKET_NAME,
  AWS_BUCKET_URI: process.env.AWS_BUCKET_URI,
  AWS_ENDPOINT: process.env.AWS_ENDPOINT,
  FIREBASE_TYPE: process.env.FIREBASE_TYPE,
  FIREBASE_PROJECT_ID: process.env.FIREBASE_PROJECT_ID,
  FIREBASE_PROJECT_KEY_ID: process.env.FIREBASE_PROJECT_KEY_ID,
  FIREBASE_PRIVATE_KEY: process.env.FIREBASE_PRIVATE_KEY,
  FIREBASE_CLIENT_EMAIL: process.env.FIREBASE_CLIENT_EMAIL,
  FIREBASE_CLIENT_ID: process.env.FIREBASE_CLIENT_ID,
  FIREBASE_AUTH_URI: process.env.FIREBASE_AUTH_URI,
  FIREBASE_TOKEN_URI: process.env.FIREBASE_TOKEN_URI,
  FIREBASE_AUTH_PROVIDER_X509_CERT_URL: process.env.FIREBASE_AUTH_PROVIDER_X509_CERT_URL,
  FIREBASE_CLIENT_X509_CERT_URL: process.env.FIREBASE_CLIENT_X509_CERT_URL,
  FIREBASE_UNIVERSE_DOMAIN: process.env.FIREBASE_UNIVERSE_DOMAIN,
};

// Validate configuration
const { error, value: validatedConfig } = configSchema.validate(_config);

if (error) {
  throw new Error(`Config validation error: ${error.message}`);
}

module.exports = validatedConfig;
