{"name": "impact-leader-backend", "version": "1.0.0", "description": "Backend API for Impact Leader Organization Registration", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["nodejs", "express", "mongodb", "api", "backend"], "author": "<PERSON><PERSON>ith<PERSON><PERSON>", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "dotenv": "^16.3.1", "compression": "^1.7.4", "morgan": "^1.10.0", "winston": "^3.11.0", "joi": "^17.11.0", "nodemailer": "^6.9.7", "aws-sdk": "^2.1498.0", "firebase-admin": "^11.11.1", "redis": "^4.6.10"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0"}, "engines": {"node": ">=16.0.0"}}