# Impact Leader Backend API

A robust Node.js backend API for Impact Leader organization registration system with comprehensive security, authentication, and monitoring features.

## Features

- 🔐 **JWT Authentication** with refresh tokens
- 🛡️ **Security** with Helmet, CORS, rate limiting
- 📊 **MongoDB** with Mongoose ODM
- ✅ **Input Validation** with express-validator
- 📝 **Logging** with Winston
- 🚀 **Error Handling** with custom error classes
- 📈 **Rate Limiting** for API protection
- 🔄 **Graceful Shutdown** handling
- 📚 **API Documentation** endpoint

## Project Structure

```
src/
├── config/
│   ├── config.js          # Environment configuration
│   └── database.js        # MongoDB connection
├── controllers/
│   ├── authController.js  # Authentication logic
│   └── organizationController.js # Organization CRUD
├── middleware/
│   ├── auth.js            # Authentication middleware
│   ├── errorHandler.js    # Global error handling
│   ├── security.js        # Security middleware
│   └── validation.js      # Input validation
├── models/
│   ├── User.js            # User model
│   ├── Organization.js    # Organization model
│   └── index.js           # Model exports
├── routes/
│   ├── authRoutes.js      # Auth endpoints
│   ├── organizationRoutes.js # Organization endpoints
│   └── index.js           # Route aggregation
├── services/
│   └── authService.js     # Authentication service
├── utils/
│   ├── constants.js       # Application constants
│   ├── logger.js          # Winston logger setup
│   └── responseHelper.js  # Response utilities
└── server.js              # Main application file
```

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd impact-leader-backend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` file with your configuration:
   ```env
   PORT=3000
   NODE_ENV=development
   DB_URI=mongodb://localhost:27017/impact_leader
   ACCESS_TOKEN_SECRET=your_access_token_secret
   REFRESH_TOKEN_SECRET=your_refresh_token_secret
   # ... other configurations
   ```

4. **Start the server**
   ```bash
   # Development
   npm run dev
   
   # Production
   npm start
   ```

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh-token` - Refresh access token
- `POST /api/auth/logout` - Logout user
- `POST /api/auth/logout-all` - Logout from all devices
- `GET /api/auth/profile` - Get user profile
- `PATCH /api/auth/profile` - Update user profile

### Organizations
- `POST /api/organizations/register` - Register organization
- `GET /api/organizations` - Get all organizations (with pagination)
- `GET /api/organizations/:id` - Get organization by ID
- `PATCH /api/organizations/:id/status` - Update organization status
- `DELETE /api/organizations/:id` - Delete organization
- `GET /api/organizations/stats` - Get organization statistics

### Utility
- `GET /api/health` - Health check
- `GET /api/docs` - API documentation

## Organization Registration Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| companyName | String | Yes | Organization name |
| organizationType | Enum | Yes | Type of organization |
| organizationTypeOther | String | Conditional | Required if type is "Other" |
| designation | Enum | Yes | User's designation |
| designationOther | String | Conditional | Required if designation is "Other" |
| themes | Array | Yes | Social sector themes |
| themesOther | Array | Conditional | Required if themes include "Other" |
| termsAccepted | Boolean | Yes | Must be true |
| contactEmail | String | No | Contact email |
| contactPhone | String | No | Contact phone |

## Security Features

- **Rate Limiting**: Multiple tiers (general, API, strict)
- **Helmet**: Security headers
- **CORS**: Cross-origin resource sharing
- **Input Validation**: Comprehensive validation rules
- **Authentication**: JWT with refresh tokens
- **Account Locking**: After failed login attempts
- **Password Hashing**: bcrypt with salt rounds

## Environment Variables

See `.env.example` for all required environment variables including:
- Database configuration
- JWT secrets
- Email settings
- AWS configuration
- Firebase configuration

## Scripts

```bash
npm start          # Start production server
npm run dev        # Start development server with nodemon
npm test           # Run tests
npm run test:watch # Run tests in watch mode
npm run lint       # Run ESLint
npm run lint:fix   # Fix ESLint issues
```

## Error Handling

The API uses a global error handler that:
- Logs all errors with Winston
- Returns appropriate HTTP status codes
- Provides detailed error messages in development
- Sanitizes error messages in production
- Handles MongoDB validation errors
- Manages JWT token errors

## Logging

Winston logger with:
- File rotation (5MB max, 5 files)
- Different log levels (error, warn, info, debug)
- Console output in development
- Structured JSON logging
- HTTP request logging with Morgan

## Rate Limiting

- **General**: 100 requests per 15 minutes
- **API**: 50 requests per 15 minutes  
- **Strict** (auth endpoints): 5 requests per 15 minutes

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Run linting and tests
6. Submit a pull request

## License

MIT License - see LICENSE file for details
